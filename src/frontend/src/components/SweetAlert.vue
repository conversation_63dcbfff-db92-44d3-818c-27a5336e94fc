<script setup>
import { inject } from "vue";

const emit = defineEmits(["confirmed", "dismissed"])

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  text: {
    type: String,
    default: null,
  },
  input: {
    type: String,
    default: null,
  },
  icon: {
    type: String,
    default: null,
  },
  confirmedText: {
    type: String,
    default: null,
  },
  showCancelButton: {
    type: Boolean,
    default: false,
  },
  cancelButtonText: {
    type: String,
    default: "Cancel",
  },
  confirmButtonText: {
    type: String,
    default: null
  },
  customClass: {
    type: Object,
    default: () => ({
      confirmButton: "btn btn-success",
      cancelButton: "btn btn-secondary",
    }),
  },
});

const swal = inject("$swal");
swal({
  title: props.title,
  text: props.text,
  input: props.input,
  icon: props.icon,
  showCancelButton: props.showCancelButton,
  cancelButtonText: props.cancelButtonText,
  confirmButtonText: props.confirmButtonText,
  customClass: {
    confirmButton: props.customClass.confirmButton,
    cancelButton: props.customClass.cancelButton,
  },
  reverseButtons: true,
}).then((result) => {
  if (result.isConfirmed) {
    emit("confirmed", result.value);
  } else if (result.isDismissed) {
    emit("dismissed", result.value);
  }
})
</script>
